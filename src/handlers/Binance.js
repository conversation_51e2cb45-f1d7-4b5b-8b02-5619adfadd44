import axios from "axios";

/**
 * Binance API Handler Class
 * Encapsulates all Binance API data fetching, caching, and timeframe management logic
 * Follows clean separation of concerns pattern for trading bot architecture
 */
class BinanceHandler {
  constructor(symbol, defaultInterval = "1h") {
    this.symbol = symbol;
    this.defaultInterval = defaultInterval;
    
    // Binance API configuration
    this.BINANCE_API = "https://api.binance.com/api/v3/klines";
    
    // Multi-timeframe configuration
    this.TIMEFRAMES = {
      "15m": { interval: "15m", limit: 96, name: "15-minute" }, // 24 hours of 15m candles
      "1h": { interval: "1h", limit: 168, name: "1-hour" }, // 7 days of 1h candles
      "4h": { interval: "4h", limit: 180, name: "4-hour" }, // 30 days of 4h candles
    };

    // Data cache for multi-timeframe analysis with TTL
    this.dataCache = {
      "15m": { data: null, lastUpdate: 0, ttl: 15 * 60 * 1000 }, // 15 min TTL
      "1h": { data: null, lastUpdate: 0, ttl: 60 * 60 * 1000 }, // 1 hour TTL
      "4h": { data: null, lastUpdate: 0, ttl: 4 * 60 * 60 * 1000 }, // 4 hour TTL
    };
  }

  /**
   * Transform raw Binance API data into standardized candle format
   * @param {Array} rawData - Raw data from Binance API
   * @param {string} timeframe - Timeframe identifier
   * @returns {Array} Standardized candle data
   */
  transformCandleData(rawData, timeframe = null) {
    return rawData.map((d) => ({
      time: d[0],
      open: parseFloat(d[1]),
      high: parseFloat(d[2]),
      low: parseFloat(d[3]),
      close: parseFloat(d[4]),
      volume: parseFloat(d[5]),
      closeTime: d[6],
      ...(timeframe && { timeframe }),
    }));
  }

  /**
   * Fetch data for a specific timeframe from Binance API
   * @param {string} timeframe - Timeframe identifier (15m, 1h, 4h)
   * @returns {Promise<Array>} Array of candle data
   */
  async fetchTimeframeData(timeframe) {
    const config = this.TIMEFRAMES[timeframe];
    if (!config) {
      throw new Error(`Invalid timeframe: ${timeframe}`);
    }

    try {
      const { data } = await axios.get(this.BINANCE_API, {
        params: {
          symbol: this.symbol,
          interval: config.interval,
          limit: config.limit,
        },
      });

      return this.transformCandleData(data, timeframe);
    } catch (error) {
      console.error(`Error fetching ${timeframe} data:`, error.message);
      throw error;
    }
  }

  /**
   * Get cached data for a timeframe or fetch fresh data if cache is expired
   * @param {string} timeframe - Timeframe identifier
   * @returns {Promise<Array>} Array of candle data
   */
  async getCachedData(timeframe) {
    const cache = this.dataCache[timeframe];
    if (!cache) {
      throw new Error(`Invalid timeframe for caching: ${timeframe}`);
    }

    const now = Date.now();

    // Check if cache is valid
    if (cache.data && now - cache.lastUpdate < cache.ttl) {
      console.log(`Using cached data for ${timeframe}`);
      return cache.data;
    }

    // Fetch fresh data
    console.log(`Fetching fresh data for ${timeframe}`);
    const data = await this.fetchTimeframeData(timeframe);

    // Update cache
    cache.data = data;
    cache.lastUpdate = now;

    return data;
  }

  /**
   * Fetch data for all configured timeframes
   * @returns {Promise<Object>} Object containing data for all timeframes
   */
  async fetchAllTimeframes() {
    try {
      const [data15m, data1h, data4h] = await Promise.all([
        this.getCachedData("15m"),
        this.getCachedData("1h"),
        this.getCachedData("4h"),
      ]);

      return {
        "15m": data15m,
        "1h": data1h,
        "4h": data4h,
      };
    } catch (error) {
      console.error("Error fetching multi-timeframe data:", error);
      throw error;
    }
  }

  /**
   * Legacy fetchData function for backward compatibility
   * Fetches data using the default interval with 240 candles
   * @returns {Promise<Array>} Array of candle data
   */
  async fetchData() {
    try {
      const { data } = await axios.get(this.BINANCE_API, {
        params: { 
          symbol: this.symbol, 
          interval: this.defaultInterval, 
          limit: 240 
        },
      });

      return this.transformCandleData(data);
    } catch (error) {
      console.error(`Error fetching legacy data for ${this.symbol}:`, error.message);
      throw error;
    }
  }

  /**
   * Get timeframe configuration
   * @param {string} timeframe - Timeframe identifier
   * @returns {Object} Timeframe configuration object
   */
  getTimeframeConfig(timeframe) {
    return this.TIMEFRAMES[timeframe];
  }

  /**
   * Get all available timeframes
   * @returns {Array} Array of timeframe identifiers
   */
  getAvailableTimeframes() {
    return Object.keys(this.TIMEFRAMES);
  }

  /**
   * Clear cache for a specific timeframe or all timeframes
   * @param {string} timeframe - Optional timeframe to clear, if not provided clears all
   */
  clearCache(timeframe = null) {
    if (timeframe) {
      if (this.dataCache[timeframe]) {
        this.dataCache[timeframe].data = null;
        this.dataCache[timeframe].lastUpdate = 0;
        console.log(`Cache cleared for ${timeframe}`);
      }
    } else {
      // Clear all caches
      Object.keys(this.dataCache).forEach(tf => {
        this.dataCache[tf].data = null;
        this.dataCache[tf].lastUpdate = 0;
      });
      console.log("All caches cleared");
    }
  }

  /**
   * Get cache status for all timeframes
   * @returns {Object} Cache status information
   */
  getCacheStatus() {
    const now = Date.now();
    const status = {};

    Object.entries(this.dataCache).forEach(([timeframe, cache]) => {
      const isValid = cache.data && (now - cache.lastUpdate < cache.ttl);
      const ageMinutes = cache.lastUpdate ? Math.floor((now - cache.lastUpdate) / (60 * 1000)) : null;
      
      status[timeframe] = {
        hasData: !!cache.data,
        isValid,
        lastUpdate: cache.lastUpdate,
        ageMinutes,
        ttlMinutes: Math.floor(cache.ttl / (60 * 1000))
      };
    });

    return status;
  }

  /**
   * Fetch data with custom parameters
   * @param {string} interval - Custom interval
   * @param {number} limit - Number of candles to fetch
   * @param {string} symbol - Optional symbol override
   * @returns {Promise<Array>} Array of candle data
   */
  async fetchCustomData(interval, limit, symbol = null) {
    try {
      const { data } = await axios.get(this.BINANCE_API, {
        params: {
          symbol: symbol || this.symbol,
          interval,
          limit,
        },
      });

      return this.transformCandleData(data);
    } catch (error) {
      console.error(`Error fetching custom data (${interval}, ${limit}):`, error.message);
      throw error;
    }
  }
}

export default BinanceHandler;
